---
description: Auto-prioritize and execute Playwright MCP smoke tests from a single base URL input. Uses the standard MCP config with HTML reports and video recordings. Site structure is dynamically extracted via login and DOM inspection.
---

# 🧪 Playwright MCP Smoke Tests – Auth + DOM-Driven Execution

You are responsible for **selecting, creating, running, and reporting Playwright MCP smoke tests**, starting from a **base URL**, and **only after inspecting the site structure through Playwright login and DOM analysis**.

---

## 🚦 Input Validation

- `$ARGUMENTS` must be a valid **base URL**.
- If provided → proceed.
- If **missing or invalid**, pause and prompt:

```
⚠️ Missing Required Input  
The smoke test run needs the following input(s): base URL.  

👉 Please provide a valid base URL (e.g., https://staging.example.com) so the smoke tests can proceed.
```

---

## 🧱 Site Structure Extraction (SPA / Login-Gated)

1. Launch a headless Playwright browser and load the base URL.
2. If no visible structure (SPA shell):
   - Attempt login with demo or provided credentials.
3. After login:
   - Extract visible **navigation links**, **product links**, **menu items**, and **page URLs** via DOM traversal.
   - Prefer **stable selectors** (`getByRole`, `getByText`, `getByLabel`) only.

### Example: https://www.saucedemo.com

| Route                            | Purpose              |
|----------------------------------|----------------------|
| `/inventory.html`                 | Product catalog      |
| `/inventory-item.html?id=X`       | Product details      |
| `/cart.html`                      | Cart                 |
| `/checkout-step-one.html`         | Checkout step 1      |
| `/checkout-step-two.html`         | Checkout step 2      |
| `/checkout-complete.html`         | Order confirmation   |
| `/`                               | Login/logout redirect|
| External: `https://saucelabs.com/`| About page           |

If structure cannot be extracted due to login failure or JS errors → **halt and report**.

---

## 🧭 Scenario Auto-Prioritization

Based on extracted DOM structure, select up to **3 high-priority smoke flows**:

---

## 🔒 Stable Selector Policy

All locators **must be stable**:

- ✅ Use `getByRole`, `getByLabel`, `getByText`  
- ✅ Use `data-testid` or ARIA attributes if available  
- ❌ Avoid dynamic IDs, brittle CSS paths, `nth-child` selectors  
- ✅ Favor semantic HTML and accessibility-first selectors

If a selector is unstable, replace it automatically with a stable fallback.

---

## 🔧 Auto-Heal Locators

When a test fails due to a missing/changed locator:

1. Re-query DOM using fallback strategies:
   - Role-based (`getByRole`)
   - Accessible labels (`getByLabel`)
   - Text queries (`getByText`, `locator:has-text()`)
2. If the same element is found with a different locator:
   - Update the script dynamically
   - Log the new selector
   - Re-run the failed test
3. If healing fails:
   - Capture error + screenshot
   - Mark scenario as failed
   - Report broken selector

---

## 🛠 Test File Generation

- Language: **TypeScript**
- Location: `./playwright_run_experiment/`
- Filename: `<slugified-scenario>.smoke.spec.ts`

**Conventions:**
- Use `@playwright/test`
- Title must include `@smoke`
- 1–3 assertions max
- Capture screenshot on failure
- Use only stable selectors
- Do not use `test.slow()`

---

## 🧰 Environment Preparation

Before running:

1. Ensure Playwright is installed:
   ```bash
   bun add -d @playwright/test
   bunx playwright install
   ```
2. Verify config exists:
   ```
   ./mcp_config/playwright.config.ts
   ```
   If missing → halt and notify user.
3. Auto-install missing dependencies (e.g., `typescript`, `ts-node`).

---

## ▶️ Execute Tests

Run with MCP config:

```bash
bunx playwright test --config ./mcp_config/playwright.config.ts
```

- ❌ Do not override flags, env vars, or config paths.

---

## 📦 Required Artifacts

Each run must produce:

- ✅ Playwright HTML report  
- ✅ Video recordings (all tests)

If either is missing → mark run as failed.

---

## 📋 Final Output Summary

Report must include:

- ✅ Chosen scenarios + short rationale  
- 📄 Path to HTML report  
- 🎞️ Paths to videos  
- 🟢 Outcome: `Passed` or `❌ Failed`  
- If failed:
  - Failing step
  - Screenshot path

---

## ✅ Acceptance Criteria

Run is **successful only if**:

1. All selected flows complete  
2. HTML report is generated  
3. Video recordings are saved  

If any are missing → run is **Failed**
