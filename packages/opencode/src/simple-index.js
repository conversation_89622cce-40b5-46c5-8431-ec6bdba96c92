#!/usr/bin/env node

// Simple standalone version of OpenCode TestVista
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import readline from 'readline';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

const VERSION = "1.0.0";

// Interactive TUI function using the real OpenCode TUI
async function startInteractiveTUI(options = {}) {
  const { fileURLToPath } = await import('url');
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);

  // Path to the TUI binary
  const tuiBinaryPath = path.join(__dirname, '..', 'bin', 'tui', 'opencode-testvista-tui');

  // Check if TUI binary exists
  if (!fs.existsSync(tuiBinaryPath)) {
    console.log('⚠️  OpenCode TestVista TUI binary not found.');
    console.log('   Falling back to simple interactive mode...');
    console.log('');
    await startSimpleInteractiveTUI();
    return;
  }

  // Set up environment variables for the TUI
  const env = {
    ...process.env,
    OPENCODE_SERVER: 'http://localhost:3000', // Default server URL
    OPENCODE_PROJECT: JSON.stringify({
      name: 'opencode-testvista',
      path: process.cwd()
    })
  };

  // Add Azure OpenAI environment variables if they exist
  if (process.env.AZURE_OPENAI_API_KEY) {
    env.AZURE_OPENAI_API_KEY = process.env.AZURE_OPENAI_API_KEY;
  }
  if (process.env.AZURE_OPENAI_ENDPOINT) {
    env.AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
  }
  if (process.env.AZURE_OPENAI_API_VERSION) {
    env.AZURE_OPENAI_API_VERSION = process.env.AZURE_OPENAI_API_VERSION;
  }

  console.log('🚀 Starting OpenCode TestVista TUI...');
  console.log('');
  console.log('⚠️  Note: The full TUI requires a server component.');
  console.log('   If the TUI fails to start, we\'ll fall back to simple mode.');
  console.log('');

  // Build command arguments
  const args = [];
  if (options.model) args.push('--model', options.model);
  if (options.prompt) args.push('--prompt', options.prompt);
  if (options.agent) args.push('--agent', options.agent);
  if (options.session) args.push('--session', options.session);

  try {
    // Spawn the TUI binary
    const tuiProcess = spawn(tuiBinaryPath, args, {
      stdio: 'inherit',
      env: env,
      cwd: process.cwd()
    });

    // Handle process events
    tuiProcess.on('error', (error) => {
      console.error('❌ Failed to start TUI:', error.message);
      console.log('   Falling back to simple interactive mode...');
      console.log('');
      startSimpleInteractiveTUI();
    });

    tuiProcess.on('exit', (code) => {
      if (code !== 0 && code !== null) {
        console.log(`\n⚠️  TUI exited with code ${code}`);
        console.log('   The full TUI requires the OpenCode server to be running.');
        console.log('   Falling back to simple interactive mode...');
        console.log('');
        startSimpleInteractiveTUI();
        return;
      }
      process.exit(code || 0);
    });

    // Handle Ctrl+C
    process.on('SIGINT', () => {
      tuiProcess.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      tuiProcess.kill('SIGTERM');
    });

  } catch (error) {
    console.error('❌ Failed to start TUI:', error.message);
    console.log('   Falling back to simple interactive mode...');
    console.log('');
    await startSimpleInteractiveTUI();
  }
}

// Fallback simple interactive TUI
async function startSimpleInteractiveTUI() {
  console.clear();
  console.log(logo());
  console.log('🚀 Welcome to OpenCode TestVista Interactive Mode!');
  console.log('');
  console.log('💡 Tips:');
  console.log('  • Type your coding questions or requests');
  console.log('  • Use "help" for available commands');
  console.log('  • Use "exit" or Ctrl+C to quit');
  console.log('  • Use "clear" to clear the screen');
  console.log('  • Use "auth" to configure Azure OpenAI');
  console.log('');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '🤖 opencode-testvista> '
  });

  rl.prompt();

  rl.on('line', async (input) => {
    const command = input.trim();

    if (command === 'exit' || command === 'quit') {
      console.log('👋 Goodbye!');
      rl.close();
      return;
    }

    if (command === 'clear') {
      console.clear();
      console.log(logo());
      rl.prompt();
      return;
    }

    if (command === 'help') {
      console.log('');
      console.log('📚 Available Commands:');
      console.log('  help     - Show this help message');
      console.log('  auth     - Configure Azure OpenAI authentication');
      console.log('  clear    - Clear the screen');
      console.log('  exit     - Exit the interactive mode');
      console.log('  version  - Show version information');
      console.log('  status   - Check configuration status');
      console.log('');
      console.log('💬 Or just type any coding question or request!');
      console.log('');
      rl.prompt();
      return;
    }

    if (command === 'auth') {
      showAuthInstructions();
      rl.prompt();
      return;
    }

    if (command === 'version') {
      console.log(`OpenCode TestVista v${VERSION}`);
      rl.prompt();
      return;
    }

    if (command === 'status') {
      checkConfigurationStatus();
      rl.prompt();
      return;
    }

    if (command === '') {
      rl.prompt();
      return;
    }

    // Handle user prompts
    console.log('');
    console.log('🔄 Processing your request...');
    console.log(`📝 Prompt: "${command}"`);
    console.log('');
    console.log('⚠️  AI processing not yet implemented in this version.');
    console.log('   This would normally send your request to Azure OpenAI.');
    console.log('');
    console.log('🔧 To enable full AI functionality:');
    console.log('   1. Configure authentication: type "auth"');
    console.log('   2. Set up your Azure OpenAI credentials');
    console.log('   3. Future updates will add the AI integration');
    console.log('');

    rl.prompt();
  });

  rl.on('close', () => {
    console.log('👋 Goodbye!');
    process.exit(0);
  });

  // Handle Ctrl+C
  rl.on('SIGINT', () => {
    console.log('');
    console.log('👋 Goodbye!');
    process.exit(0);
  });
}

function showAuthInstructions() {
  console.log('');
  console.log('🔧 Azure OpenAI Authentication Setup');
  console.log('');
  console.log('Please set the following environment variables:');
  console.log('');
  console.log('export AZURE_OPENAI_API_KEY="your-api-key-here"');
  console.log('export AZURE_OPENAI_ENDPOINT="https://testvista.openai.azure.com/"');
  console.log('export AZURE_OPENAI_API_VERSION="2025-01-01-preview"');
  console.log('');
  console.log('Or create a .env file in your project directory with:');
  console.log('AZURE_OPENAI_API_KEY=your-api-key-here');
  console.log('AZURE_OPENAI_ENDPOINT=https://testvista.openai.azure.com/');
  console.log('AZURE_OPENAI_API_VERSION=2025-01-01-preview');
  console.log('');
}

function checkConfigurationStatus() {
  console.log('');
  console.log('🔍 Configuration Status:');
  console.log('');

  const hasApiKey = !!process.env.AZURE_OPENAI_API_KEY;
  const hasEndpoint = !!process.env.AZURE_OPENAI_ENDPOINT;
  const hasVersion = !!process.env.AZURE_OPENAI_API_VERSION;

  console.log(`  API Key:    ${hasApiKey ? '✅ Set' : '❌ Not set'}`);
  console.log(`  Endpoint:   ${hasEndpoint ? '✅ Set' : '❌ Not set'}`);
  console.log(`  Version:    ${hasVersion ? '✅ Set' : '❌ Not set'}`);
  console.log('');

  if (hasApiKey && hasEndpoint && hasVersion) {
    console.log('🎉 All credentials are configured!');
  } else {
    console.log('⚠️  Some credentials are missing. Run "auth" for setup instructions.');
  }
  console.log('');
}

const logo = () => `
 ██████╗ ██████╗ ███████╗███╗   ██╗ ██████╗ ██████╗ ██████╗ ███████╗
██╔═══██╗██╔══██╗██╔════╝████╗  ██║██╔════╝██╔═══██╗██╔══██╗██╔════╝
██║   ██║██████╔╝█████╗  ██╔██╗ ██║██║     ██║   ██║██║  ██║█████╗  
██║   ██║██╔═══╝ ██╔══╝  ██║╚██╗██║██║     ██║   ██║██║  ██║██╔══╝  
╚██████╔╝██║     ███████╗██║ ╚████║╚██████╗╚██████╔╝██████╔╝███████╗
 ╚═════╝ ╚═╝     ╚══════╝╚═╝  ╚═══╝ ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝
                                                                     
TestVista Edition - AI-powered coding assistant
`;

const cli = yargs(hideBin(process.argv))
  .scriptName("opencode-testvista")
  .help("help", "show help")
  .version("version", "show version number", VERSION)
  .alias("version", "v")
  .usage("\n" + logo())
  .command({
    command: 'tui',
    describe: 'Start interactive TUI mode',
    builder: (yargs) => {
      return yargs
        .option('model', {
          describe: 'AI model to use',
          type: 'string'
        })
        .option('prompt', {
          describe: 'Initial prompt',
          type: 'string'
        })
        .option('agent', {
          describe: 'Agent to use',
          type: 'string'
        })
        .option('session', {
          describe: 'Session ID to continue',
          type: 'string'
        });
    },
    handler: async (argv) => {
      await startInteractiveTUI({
        model: argv.model,
        prompt: argv.prompt,
        agent: argv.agent,
        session: argv.session
      });
    }
  })
  .command({
    command: 'auth',
    describe: 'Configure Azure OpenAI authentication',
    handler: () => {
      showAuthInstructions();
    }
  })
  .command({
    command: ['run [prompt]', '$0 [prompt]'],
    describe: 'Run OpenCode TestVista with a prompt',
    builder: (yargs) => {
      return yargs
        .positional('prompt', {
          describe: 'The prompt to send to the AI',
          type: 'string'
        })
        .option('model', {
          describe: 'AI model to use',
          type: 'string',
          default: 'gpt-4'
        })
        .option('debug', {
          describe: 'Enable debug mode',
          type: 'boolean',
          default: false
        });
    },
    handler: async (argv) => {
      if (!argv.prompt) {
        // Start interactive TUI mode when no prompt is provided
        await startInteractiveTUI();
        return;
      }
      
      console.log('🚀 Running OpenCode TestVista...');
      console.log('');
      console.log('Prompt:', argv.prompt);
      console.log('Model:', argv.model);
      console.log('');
      console.log('⚠️  This is a simplified version of OpenCode TestVista.');
      console.log('The full AI functionality is not yet implemented in this npm package.');
      console.log('');
      console.log('Please ensure your Azure OpenAI credentials are configured:');
      console.log('Run: opencode-testvista auth');
    }
  })
  .fail((msg) => {
    if (msg.startsWith("Unknown argument") || msg.startsWith("Not enough non-option arguments")) {
      cli.showHelp("log");
    }
    process.exit(1);
  })
  .strict();

export async function main() {
  try {
    await cli.parse();
  } catch (e) {
    console.error('Error:', e.message);
    process.exit(1);
  }
}

// If this file is run directly, execute main
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
