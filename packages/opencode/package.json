{"$schema": "https://json.schemastore.org/package.json", "version": "0.6.3", "name": "opencode", "type": "module", "private": true, "scripts": {"typecheck": "tsc --noEmit", "dev": "bun run --conditions=development ./src/index.ts"}, "bin": {"opencode": "./bin/opencode"}, "exports": {"./*": "./src/*.ts"}, "devDependencies": {"@ai-sdk/amazon-bedrock": "2.2.10", "@octokit/webhooks-types": "7.6.1", "@standard-schema/spec": "1.0.0", "@tsconfig/bun": "1.0.7", "@types/bun": "latest", "@types/turndown": "5.0.5", "@types/yargs": "17.0.33", "typescript": "5.8.2", "vscode-languageserver-types": "3.17.5", "zod-to-json-schema": "3.24.5"}, "dependencies": {"@clack/prompts": "1.0.0-alpha.1", "@hono/zod-validator": "0.4.2", "@modelcontextprotocol/sdk": "1.15.1", "@openauthjs/openauth": "0.4.3", "@opencode-ai/plugin": "*", "@opencode-ai/sdk": "*", "@standard-schema/spec": "1.0.0", "@zip.js/zip.js": "2.7.62", "ai": "5.0.8", "decimal.js": "10.5.0", "diff": "8.0.2", "gray-matter": "4.0.3", "hono": "4.7.10", "hono-openapi": "0.4.8", "ignore": "7.0.5", "jsonc-parser": "3.3.1", "minimatch": "10.0.3", "open": "10.1.2", "remeda": "2.26.0", "tree-sitter": "0.22.4", "tree-sitter-bash": "0.23.3", "turndown": "7.2.0", "ulid": "3.0.1", "vscode-jsonrpc": "8.2.1", "web-tree-sitter": "0.22.6", "xdg-basedir": "5.1.0", "yargs": "18.0.0", "zod": "3.25.76", "zod-openapi": "4.1.0"}}